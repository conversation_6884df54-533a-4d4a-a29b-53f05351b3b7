'use client';
import { useAuth } from '@/context/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  UsersIcon,
  ShoppingBagIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  VideoCameraIcon
} from '@heroicons/react/24/outline';
import { userService } from '@/services/userService';
import { useEffect, useState } from 'react';
import productService from '@/lib/api/productService';
import orderService from '@/lib/api/orderService';
import videoBlogService from '@/lib/api/videoBlogService';


export default function Dashboard() {
  const [totalUsers, setTotalUsers] = useState(0);
  const [totalProducts, setTotalProducts] = useState(0);
  const [totalOrders, setTotalOrders] = useState(0);
  const [totalRevenue, setTotalRevenue] = useState(0);
  const [totalVideoBlogs, setTotalVideoBlogs] = useState(0);
  const [recentActivities, setRecentActivities] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  
useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true);
      try {
        // Fetch all data in parallel
        const [usersResponse, productsResponse, orderStats, videoBlogsResponse] = await Promise.all([
          userService.getUsers().catch(err => {
            console.error('Error fetching users:', err);
            return [];
          }),
          productService.getProducts().catch(err => {
            console.error('Error fetching products:', err);
            return [];
          }),
          orderService.getOrderStats().catch(err => {
            console.error('Error fetching order stats:', err);
            return { totalOrders: 0, totalRevenue: 0 };
          }),
          videoBlogService.getVideoBlogs({ limit: 1000 }).catch(err => {
            console.error('Error fetching video blogs:', err);
            return { data: { videoBlogs: [] } };
          })
        ]);

        // Update state with fetched data
        setTotalUsers(Array.isArray(usersResponse) ? usersResponse.length : 0);
        setTotalProducts(Array.isArray(productsResponse) ? productsResponse.length : 0);
        setTotalOrders(orderStats.totalOrders || 0);
        setTotalRevenue(orderStats.totalRevenue || 0);
        setTotalVideoBlogs(videoBlogsResponse.data?.videoBlogs?.length || 0);

        // Create recent activities from available data
        const activities = [];

        // Add recent users (last 5)
        if (Array.isArray(usersResponse) && usersResponse.length > 0) {
          const recentUsers = usersResponse
            .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
            .slice(0, 3);

          recentUsers.forEach(user => {
            activities.push({
              id: `user-${user.id}`,
              user: {
                name: user.name,
                initials: user.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
              },
              action: 'Registered account',
              time: new Date(user.createdAt),
              status: 'success',
              type: 'user'
            });
          });
        }

        // Add recent video blogs
        if (videoBlogsResponse.data?.videoBlogs?.length > 0) {
          const recentVideos = videoBlogsResponse.data.videoBlogs
            .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
            .slice(0, 2);

          recentVideos.forEach(video => {
            activities.push({
              id: `video-${video.id}`,
              user: {
                name: 'Admin',
                initials: 'AD'
              },
              action: `Published video: ${video.title.substring(0, 30)}...`,
              time: new Date(video.createdAt),
              status: 'success',
              type: 'video'
            });
          });
        }

        // Sort activities by time and take the most recent 5
        const sortedActivities = activities
          .sort((a, b) => b.time.getTime() - a.time.getTime())
          .slice(0, 5);

        setRecentActivities(sortedActivities);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds} seconds ago`;
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    switch (status) {
      case 'success':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'warning':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'error':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getAvatarColor = (type: string) => {
    switch (type) {
      case 'user':
        return 'from-blue-500 to-blue-600';
      case 'video':
        return 'from-purple-500 to-purple-600';
      case 'order':
        return 'from-green-500 to-green-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <DashboardLayout>
      <div className="mb-4 sm:mb-6 lg:mb-8">
        {/* Welcome Banner */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg sm:rounded-xl p-3 sm:p-4 md:p-6 lg:p-8 text-white shadow-custom-lg">
          <h1 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold mb-1 sm:mb-2">
            Welcome back, {user?.name}! 👋
          </h1>
          <p className="text-blue-100 text-sm sm:text-base lg:text-lg">Here&apos;s what&apos;s happening with your admin dashboard today.</p>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-4 sm:mb-6 lg:mb-8">
        <div className="bg-white p-3 sm:p-4 md:p-6 rounded-lg sm:rounded-xl shadow-custom border-l-4 border-blue-500 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="text-xs sm:text-sm font-medium text-gray-600 uppercase tracking-wider">Total Users</h3>
              <p className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mt-1 sm:mt-2 truncate">
                {isLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                ) : (
                  totalUsers.toLocaleString()
                )}
              </p>
              <p className="text-xs sm:text-sm text-gray-500 mt-1">Registered users</p>
            </div>
            <div className="p-2 sm:p-3 bg-blue-100 rounded-lg flex-shrink-0 ml-2 sm:ml-3">
              <UsersIcon className="w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8 text-blue-600" />
            </div>
          </div>
        </div>
        <div className="bg-white p-3 sm:p-4 md:p-6 rounded-lg sm:rounded-xl shadow-custom border-l-4 border-green-500 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="text-xs sm:text-sm font-medium text-gray-600 uppercase tracking-wider">Products</h3>
              <p className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mt-1 sm:mt-2 truncate">
                {isLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                ) : (
                  totalProducts.toLocaleString()
                )}
              </p>
              <p className="text-xs sm:text-sm text-gray-500 mt-1">Available products</p>
            </div>
            <div className="p-2 sm:p-3 bg-green-100 rounded-lg flex-shrink-0 ml-2 sm:ml-3">
              <ShoppingBagIcon className="w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8 text-green-600" />
            </div>
          </div>
        </div>
        <div className="bg-white p-3 sm:p-4 md:p-6 rounded-lg sm:rounded-xl shadow-custom border-l-4 border-yellow-500 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="text-xs sm:text-sm font-medium text-gray-600 uppercase tracking-wider">Orders</h3>
              <p className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mt-1 sm:mt-2 truncate">
                {isLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                ) : (
                  totalOrders.toLocaleString()
                )}
              </p>
              <p className="text-xs sm:text-sm text-gray-500 mt-1">Total orders</p>
            </div>
            <div className="p-2 sm:p-3 bg-yellow-100 rounded-lg flex-shrink-0 ml-2 sm:ml-3">
              <ClipboardDocumentListIcon className="w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8 text-yellow-600" />
            </div>
          </div>
        </div>
        <div className="bg-white p-3 sm:p-4 md:p-6 rounded-lg sm:rounded-xl shadow-custom border-l-4 border-purple-500 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="text-xs sm:text-sm font-medium text-gray-600 uppercase tracking-wider">Revenue</h3>
              <p className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mt-1 sm:mt-2 truncate">
                {isLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-20 rounded"></div>
                ) : (
                  `$${totalRevenue.toLocaleString()}`
                )}
              </p>
              <p className="text-xs sm:text-sm text-gray-500 mt-1">Total revenue</p>
            </div>
            <div className="p-2 sm:p-3 bg-purple-100 rounded-lg flex-shrink-0 ml-2 sm:ml-3">
              <CurrencyDollarIcon className="w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Additional Stats Row */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6 mb-4 sm:mb-6 lg:mb-8">
        <div className="bg-white p-3 sm:p-4 md:p-6 rounded-lg sm:rounded-xl shadow-custom border-l-4 border-indigo-500 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="text-xs sm:text-sm font-medium text-gray-600 uppercase tracking-wider">Video Blogs</h3>
              <p className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mt-1 sm:mt-2 truncate">
                {isLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                ) : (
                  totalVideoBlogs.toLocaleString()
                )}
              </p>
              <p className="text-xs sm:text-sm text-gray-500 mt-1">Published videos</p>
            </div>
            <div className="p-2 sm:p-3 bg-indigo-100 rounded-lg flex-shrink-0 ml-2 sm:ml-3">
              <VideoCameraIcon className="w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8 text-indigo-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg sm:rounded-xl shadow-custom overflow-hidden">
        <div className="px-3 sm:px-4 md:px-6 py-3 sm:py-4 border-b" style={{ borderColor: 'var(--border-color)' }}>
          <h3 className="text-base sm:text-lg md:text-xl font-semibold text-gray-900">Recent Activity</h3>
          <p className="text-xs sm:text-sm text-gray-600 mt-1">Latest user activities and system events</p>
        </div>

        {/* Desktop Table View */}
        <div className="hidden md:block overflow-x-auto">
          <table className="min-w-full divide-y" style={{ borderColor: 'var(--border-color)' }}>
            <thead style={{ backgroundColor: 'var(--neutral-50)' }}>
              <tr>
                <th className="px-4 md:px-6 py-3 md:py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">User</th>
                <th className="px-4 md:px-6 py-3 md:py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Action</th>
                <th className="px-4 md:px-6 py-3 md:py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Time</th>
                <th className="px-4 md:px-6 py-3 md:py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y" style={{ borderColor: 'var(--border-color)' }}>
              {isLoading ? (
                // Loading skeleton
                [...Array(3)].map((_, index) => (
                  <tr key={index} className="hover:bg-slate-50 transition-colors">
                    <td className="px-4 md:px-6 py-3 md:py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-7 h-7 md:w-8 md:h-8 rounded-full bg-gray-200 animate-pulse mr-3"></div>
                        <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                      </div>
                    </td>
                    <td className="px-4 md:px-6 py-3 md:py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                    </td>
                    <td className="px-4 md:px-6 py-3 md:py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
                    </td>
                    <td className="px-4 md:px-6 py-3 md:py-4 whitespace-nowrap">
                      <div className="h-6 bg-gray-200 rounded-full w-16 animate-pulse"></div>
                    </td>
                  </tr>
                ))
              ) : recentActivities.length > 0 ? (
                recentActivities.map((activity) => (
                  <tr key={activity.id} className="hover:bg-slate-50 transition-colors">
                    <td className="px-4 md:px-6 py-3 md:py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className={`w-7 h-7 md:w-8 md:h-8 rounded-full bg-gradient-to-r ${getAvatarColor(activity.type)} flex items-center justify-center text-white font-semibold text-sm mr-3`}>
                          {activity.user.initials}
                        </div>
                        <span className="font-medium text-gray-900 text-sm md:text-base">{activity.user.name}</span>
                      </div>
                    </td>
                    <td className="px-4 md:px-6 py-3 md:py-4 whitespace-nowrap text-gray-900 text-sm md:text-base">{activity.action}</td>
                    <td className="px-4 md:px-6 py-3 md:py-4 whitespace-nowrap text-gray-500 text-sm">{formatTimeAgo(activity.time)}</td>
                    <td className="px-4 md:px-6 py-3 md:py-4 whitespace-nowrap">
                      <span className={getStatusBadge(activity.status)}>
                        {activity.status.charAt(0).toUpperCase() + activity.status.slice(1)}
                      </span>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-4 md:px-6 py-8 text-center text-gray-500">
                    <div className="flex flex-col items-center">
                      <div className="text-4xl mb-2">📊</div>
                      <p className="text-sm">No recent activity to display</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Mobile Card View */}
        <div className="md:hidden space-y-3">
          <div className="p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="flex items-start space-x-3">
              <div className="w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-semibold text-sm">
                JD
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <p className="text-sm font-medium text-gray-900 truncate">John Doe</p>
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Success
                  </span>
                </div>
                <p className="text-sm text-gray-600">Logged in</p>
                <p className="text-xs text-gray-500 mt-1">2 minutes ago</p>
              </div>
            </div>
          </div>
          <div className="p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="flex items-start space-x-3">
              <div className="w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
                JS
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <p className="text-sm font-medium text-gray-900 truncate">Jane Smith</p>
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Updated
                  </span>
                </div>
                <p className="text-sm text-gray-600">Updated profile</p>
                <p className="text-xs text-gray-500 mt-1">15 minutes ago</p>
              </div>
            </div>
          </div>
          <div className="p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="flex items-start space-x-3">
              <div className="w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center text-white font-semibold text-sm">
                MJ
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <p className="text-sm font-medium text-gray-900 truncate">Mike Johnson</p>
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Created
                  </span>
                </div>
                <p className="text-sm text-gray-600">Created new account</p>
                <p className="text-xs text-gray-500 mt-1">1 hour ago</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
